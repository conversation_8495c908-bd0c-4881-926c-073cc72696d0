export interface ReceetTransaction {
  ticketNumber: number;
  productName: string;
  productId: string;
  date: string;
  amount: string;
  paymentMode: string;
  status: string;
  productImage: string;
  rating: number;
  brandName: string;
  brandLogo: string;
  products?: ReceetProduct[];
  quantity?: number;
  userId: string; // Email of the user who owns this transaction
}

export interface ReceetProduct {
  name: string;
  price: string;
}

export interface ReceetData {
  id: string;
  userId: string; // Email of the user who owns this data
  transactions: ReceetTransaction[];
  totalTransactions: number;
  totalAmount: number;
  currency: string;
  lastUpdated: Date;
  filters: ReceetFilters;
  pagination: ReceetPagination;
}

export interface ReceetFilters {
  searchQuery: string;
  statusFilter: string[];
  brandFilter: string[];
  paymentModeFilter: string[];
  dateRange: {
    startDate: Date | null;
    endDate: Date | null;
  };
  amountRange: {
    minAmount: number | null;
    maxAmount: number | null;
  };
}

export interface ReceetPagination {
  currentPage: number;
  entriesPerPage: number;
  totalPages: number;
  totalEntries: number;
}

export interface ReceetState {
  receetData: ReceetData | null;
  transactions: ReceetTransaction[];
  filteredTransactions: ReceetTransaction[];
  displayedTransactions: ReceetTransaction[];
  selectedTickets: Set<number>;
  deletedReceiptIds: Set<number>; // Track deleted receipt IDs without removing transaction data
  isLoading: boolean;
  error: string | null;
  lastSync: Date | null;
}

export interface ReceetAnalytics {
  totalSpent: number;
  transactionCount: number;
  averageTransactionAmount: number;
  topBrands: BrandAnalytics[];
  paymentMethodBreakdown: PaymentMethodAnalytics[];
  monthlySpending: MonthlySpendingAnalytics[];
  statusBreakdown: StatusAnalytics[];
}

export interface BrandAnalytics {
  brandName: string;
  transactionCount: number;
  totalAmount: number;
  percentage: number;
  averageRating: number;
}

export interface PaymentMethodAnalytics {
  paymentMode: string;
  transactionCount: number;
  totalAmount: number;
  percentage: number;
}

export interface MonthlySpendingAnalytics {
  month: string;
  year: number;
  totalAmount: number;
  transactionCount: number;
  averageAmount: number;
}

export interface StatusAnalytics {
  status: string;
  count: number;
  percentage: number;
}

// Enums for better type safety
export enum ReceetStatus {
  New = 'New',
  InProgress = 'In progress',
  Loyal = 'Loyal'
}

export enum PaymentMode {
  Cash = 'Cash',
  CreditCard = 'Credit Card',
  TransferBank = 'Transfer Bank',
  CashOnDelivery = 'Cash on Delivery',
  Visa = 'Visa',
  MasterCard = 'MasterCard',
  AmericanExpress = 'American Express',
  Discover = 'Discover',
  PayPal = 'PayPal',
  ApplePay = 'Apple Pay',
  GooglePay = 'Google Pay'
}

// Type guards for ReceetTransaction interfaces
export function isReceetTransaction(obj: any): obj is ReceetTransaction {
  return obj && typeof obj === 'object' && 
         'ticketNumber' in obj && 
         'productName' in obj && 
         'userId' in obj &&
         typeof obj.ticketNumber === 'number' &&
         typeof obj.productName === 'string' &&
         typeof obj.userId === 'string';
}

export function isReceetData(obj: any): obj is ReceetData {
  return obj && typeof obj === 'object' && 
         'id' in obj && 
         'userId' in obj && 
         'transactions' in obj &&
         Array.isArray(obj.transactions);
}

export function isReceetState(obj: any): obj is ReceetState {
  return obj && typeof obj === 'object' && 
         'transactions' in obj && 
         'isLoading' in obj &&
         Array.isArray(obj.transactions) &&
         typeof obj.isLoading === 'boolean';
}

export function isReceetProduct(obj: any): obj is ReceetProduct {
  return obj && typeof obj === 'object' && 
         'name' in obj && 
         'price' in obj &&
         typeof obj.name === 'string' &&
         typeof obj.price === 'string';
}

// Helper functions for data manipulation
export function parseTransactionAmount(amount: string): number {
  const numericValue = amount.replace(/[^\d.-]/g, '');
  return parseFloat(numericValue) || 0;
}

export function formatTransactionAmount(amount: number, currency: string = 'TND'): string {
  // Format without trailing zeros
  const formatted = amount % 1 === 0 ? amount.toString() : amount.toFixed(2).replace(/\.?0+$/, '');
  return `${formatted} ${currency}`;
}

export function getTransactionsByUser(transactions: ReceetTransaction[], userId: string): ReceetTransaction[] {
  return transactions.filter(transaction => transaction.userId === userId);
}

export function getTransactionsByDateRange(
  transactions: ReceetTransaction[], 
  startDate: Date, 
  endDate: Date
): ReceetTransaction[] {
  return transactions.filter(transaction => {
    const transactionDate = new Date(transaction.date);
    return transactionDate >= startDate && transactionDate <= endDate;
  });
}

export function calculateTransactionTotal(transactions: ReceetTransaction[]): number {
  return transactions.reduce((total, transaction) => {
    return total + parseTransactionAmount(transaction.amount);
  }, 0);
}

// Helper functions for deleted receipts management
export function filterOutDeletedReceipts(
  transactions: ReceetTransaction[],
  deletedReceiptIds: Set<number>
): ReceetTransaction[] {
  return transactions.filter(transaction => !deletedReceiptIds.has(transaction.ticketNumber));
}

export function getVisibleTransactions(
  transactions: ReceetTransaction[],
  deletedReceiptIds: Set<number>
): ReceetTransaction[] {
  return filterOutDeletedReceipts(transactions, deletedReceiptIds);
}

export function isReceiptDeleted(ticketNumber: number, deletedReceiptIds: Set<number>): boolean {
  return deletedReceiptIds.has(ticketNumber);
}

// Default configuration
export const DEFAULT_RECEET_FILTERS: ReceetFilters = {
  searchQuery: '',
  statusFilter: [],
  brandFilter: [],
  paymentModeFilter: [],
  dateRange: {
    startDate: null,
    endDate: null
  },
  amountRange: {
    minAmount: null,
    maxAmount: null
  }
};

export const DEFAULT_RECEET_PAGINATION: ReceetPagination = {
  currentPage: 1,
  entriesPerPage: 10,
  totalPages: 0,
  totalEntries: 0
};

// Brand configuration for collaborating brands
export const COLLABORATING_BRANDS = [
  'Carrefour',
  'Zen',
  'Monoprix',
  'Aziza',
  'STRASS SOUSSE MALL',
  'Strass Sahloul'
];

export function isCollaboratingBrand(brandName: string): boolean {
  return COLLABORATING_BRANDS.includes(brandName);
}
